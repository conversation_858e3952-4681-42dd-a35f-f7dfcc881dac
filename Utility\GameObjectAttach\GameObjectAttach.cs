using UnityEngine;
using Nirvana;
using System.Collections.Generic;
using UnityEngine.Events;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Game
{
    [ExecuteInEditMode]
    public class GameObjectAttach : MonoBehaviour
    {
        public float delayTime = 0;
        private bool isDisableEffect = false;
        private int effectOverrideQualityLevel = -1;

        public string BundleName
        {
            get { return this.asset.BundleName; }
            set { this.asset.BundleName = value; }
        }

        public string AssetName
        {
            get { return this.asset.AssetName; }
            set { this.asset.AssetName = value; }
        }

#if UNITY_EDITOR
        public string AssetGuid
        {
            get { return this.asset.AssetGUID; }
            set { this.asset.AssetGUID = value; }
        }
#endif

        [SerializeField]
        private AssetID asset;
        /// <summary>
        /// 获取或设置asset
        /// </summary>
        public AssetID Asset
        {
            get
            {
                return this.asset;
            }

            set
            {
                if (!this.asset.Equals(value))
                {
                    this.asset = value;
                }
            }
        }

        public bool IsSyncLayer = true;

#if UNITY_EDITOR
        private GameObject previewGameObj;
#endif

        private void Awake()
        {
            ConfoundMgr.RunRubbishCS(UnityEngine.Random.Range(2, 11));
        }

        private void OnDestroy()
        {
            if (EventDispatcher.Instance != null)
            {
                EventDispatcher.Instance.OnGameObjAttachDestroyed(this);
            }
        }

        private bool m_disable = false;

        private void OnDisable()
        {
#if UNITY_EDITOR
            DestroyAttachObj();
            //ActiveMonitor.OnDisableObj(this.gameObject, "GameObjectAttach");
#endif

            if (EventDispatcher.Instance != null)
            {
                EventDispatcher.Instance.OnGameObjAttachDisable(this);
            }

            m_disable = true;
        }

        private void OnEnable()
        {
            UpdateAttachObj();
            m_disable = false;
#if UNITY_EDITOR
            //ActiveMonitor.OnEnableObj(this.gameObject, "GameObjectAttach");
#endif
        }

        private void UpdateAttachObj()
        {
#if UNITY_EDITOR
			var prefab_stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
			if (prefab_stage != null)
			{
				return;
			}

			CreateAttachObj();
#endif

			if (this.isDisableEffect)
            {
                return;
            }

            Scheduler.Delay(() =>
            {
                if (null != this && this.enabled && !this.isDisableEffect && !m_disable)
                {
                    if (EventDispatcher.Instance != null)
                    {
                        if (gameObject.activeSelf)
                        {
                            EventDispatcher.Instance.OnGameObjAttachEnable(this);
                        }
                    }
                }
            }, delayTime);
        }

        //  因为加载是在lua里，在lua里回调加载完成
        public void OnLoadComplete(GameObject effect)
        {
            var controlActive = effect.GetComponent<QualityControlActive>();
            if (null != controlActive)
            {
                if (effectOverrideQualityLevel != -1)
                {
                    controlActive.SetOverrideLevel(effectOverrideQualityLevel);
                }
                else
                {
                    controlActive.ResetOverrideLevel();
                }
            }
        }

        public void SetIsDisableEffect(bool isDisableEffect)
        {
            this.isDisableEffect = isDisableEffect;
            if (EventDispatcher.Instance != null)
            {
                if (isDisableEffect)
                {
                    EventDispatcher.Instance.OnGameObjAttachDisable(this);
                }
                else
                {
                    UpdateAttachObj();
                }
            }
        }

        public void SetIsSceneOptimize(bool isDisableEffect, int effectOverrideQualityLevel = -1)
        {
            this.effectOverrideQualityLevel = effectOverrideQualityLevel;
            this.SetIsDisableEffect(isDisableEffect);
        }

        public bool IsSceneOptimize()
        {
            return this.isDisableEffect;
        }

#if UNITY_EDITOR

        private bool dirty = false;

        private void OnValidate()
        {
            dirty = true;
        }

        private void Update()
        {
            if (GameRoot.Instance != null)
            {
                return;
            }

            if (dirty)
            {
                dirty = false;
                CreateAttachObj();
            }
        }

        private void DestroyAttachObj()
        {
            if (GameRoot.Instance == null)
            {
                var previewObject = this.gameObject.GetComponent<Nirvana.PreviewObject>();
                if (previewObject)
                {
                    previewObject.ClearPreview();
                }

                if (previewGameObj != null)
                {
                    Destroy(previewGameObj);
                    previewGameObj = null;
                }
            }
        }

        private void CreateAttachObj()
        {
            DestroyAttachObj();

            if (GameRoot.Instance == null)
            {
                if (!string.IsNullOrEmpty(BundleName) &&
                    !string.IsNullOrEmpty(AssetName))
                {
                    var asset = EditorResourceMgr.LoadGameObject(BundleName, AssetName);
                    if (asset != null)
                    {
                        var go = Instantiate<GameObject>(asset);
                        if (Application.isPlaying)
                        {
                            go.transform.SetParent(this.transform, false);
                            previewGameObj = go;
                        }
                        else
                        {
                            var previewObj = this.gameObject.GetComponent<Nirvana.PreviewObject>() ?? this.gameObject.AddComponent<Nirvana.PreviewObject>();
                            previewObj.SimulateInEditMode = true;
                            previewObj.SetPreview(go);
                        }
                    }
                }
            }
        }

        public void RefreshAssetBundleName()
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(this.AssetGuid);
            var importer = AssetImporter.GetAtPath(assetPath);
            if (null != importer)
            {
                this.BundleName = importer.assetBundleName;
                this.AssetName = assetPath.Substring(assetPath.LastIndexOf("/") + 1);
            }
        }

        public bool IsGameobjectMissing()
        {
            string assetPath = AssetDatabase.GUIDToAssetPath(this.AssetGuid);
            var importer = AssetImporter.GetAtPath(assetPath);
            if (null == importer)
            {
                return true;
            }

            if (this.BundleName != importer.assetBundleName
                || this.AssetName != assetPath.Substring(assetPath.LastIndexOf("/") + 1))
            {
                return true;
            }

            return false;
        }
#endif

    }
}
