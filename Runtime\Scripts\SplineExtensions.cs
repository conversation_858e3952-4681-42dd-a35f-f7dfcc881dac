﻿using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Splines;

namespace UI_Spline_Renderer
{
    /// <summary>
    /// 样条线扩展方法类
    /// 提供用于样条线节点重新定向和平滑处理的扩展方法
    /// </summary>
    public static class SplineExtensions
    {
        /// <summary>
        /// 将单个节点重新定向到屏幕方向
        /// 调整节点的旋转使其面向屏幕（Z轴负方向）
        /// </summary>
        /// <param name="spline">要操作的样条线</param>
        /// <param name="index">节点索引</param>
        /// <param name="withoutNotify">是否不触发通知事件</param>
        public static void ReorientKnot(this Spline spline, int index, bool withoutNotify = false)
        {
            var knot = spline[index];
            var rot = (Quaternion)knot.Rotation;

            var forward = rot * Vector3.forward;
            var projected = Vector3.ProjectOnPlane(forward, Vector3.back);
            var targetRotation = Quaternion.LookRotation(projected, Vector3.back);
            knot.Rotation = targetRotation;

            if(withoutNotify)spline.SetKnotNoNotify(index, knot);
            else spline.SetKnot(index, knot);
        }

        /// <summary>
        /// 将所有节点重新定向到屏幕方向
        /// 遍历样条线容器中的所有样条线，将每个节点都面向屏幕
        /// </summary>
        /// <param name="container">样条线容器</param>
        /// <param name="withoutNotify">是否不触发通知事件</param>
        public static void ReorientKnots(this SplineContainer container, bool withoutNotify = false)
        {
            foreach (var spline in container.Splines)
            {
                for (int i = 0; i < spline.Count; i++)
                {
                    ReorientKnot(spline, i, withoutNotify);
                }
            }
        }



        /// <summary>
        /// 将所有节点重新定向到屏幕方向并应用自动平滑
        /// 不仅调整节点朝向，还根据相邻节点位置自动计算平滑的切线
        /// 使样条线在UI环境下具有更好的视觉效果
        /// </summary>
        /// <param name="container">样条线容器</param>
        public static void ReorientKnotsAndSmooth(this SplineContainer container)
        {
            foreach (var spline in container.Splines)
            {
                for (int i = 0; i < spline.Count; i++)
                {
                    var knot = spline[i];
                    var prev = i == 0 ? knot.Position : spline[i - 1].Position;
                    var next = i == spline.Count - 1 ? spline[i].Position : spline[i + 1].Position;

                    knot = SplineUtility.GetAutoSmoothKnot(knot.Position, prev, next, new float3(0, 0, -1));
                    spline.SetKnot(i, knot);
                }
            }
        }

    }
}