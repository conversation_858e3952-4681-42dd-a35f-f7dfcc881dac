﻿using System;
using System.Security.Cryptography;
using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Mathematics;
using UnityEngine;

namespace UI_Spline_Renderer
{
    /// <summary>
    /// 原生动画曲线结构
    /// 用于在Job System中高效处理动画曲线计算
    /// 支持多种构造方式和内存管理
    /// </summary>
    internal readonly struct NativeCurve : IDisposable
    {
        private const int TRUE = 1;
        private const int FALSE = 0;
        /// <summary>关键帧数组</summary>
        public readonly NativeArray<Keyframe> Keys;
        /// <summary>是否拥有内存所有权</summary>
        private readonly int owner;

        /// <summary>
        /// 从Unity AnimationCurve构造原生曲线
        /// </summary>
        /// <param name="c">Unity动画曲线</param>
        /// <param name="alloc">内存分配器</param>
        public NativeCurve(AnimationCurve c, Allocator alloc)
        {
            Keys = new NativeArray<Keyframe>(c.keys, alloc);
            owner = TRUE;
        }

        /// <summary>
        /// 构造指定大小的空原生曲线
        /// </summary>
        /// <param name="size">关键帧数量</param>
        /// <param name="alloc">内存分配器</param>
        public NativeCurve(int size, Allocator alloc)
        {
            Keys = new NativeArray<Keyframe>(size, alloc);
            owner = TRUE;
        }

        /// <summary>
        /// 从关键帧数组构造原生曲线
        /// </summary>
        /// <param name="keyframes">关键帧数组</param>
        /// <param name="alloc">内存分配器</param>
        public NativeCurve(Keyframe[] keyframes, Allocator alloc)
        {
            Keys = new NativeArray<Keyframe>(keyframes, alloc);
            owner = TRUE;
        }

        /// <summary>
        /// 从原生关键帧数组构造原生曲线（复制）
        /// </summary>
        /// <param name="keyframes">原生关键帧数组</param>
        /// <param name="alloc">内存分配器</param>
        public NativeCurve(NativeArray<Keyframe> keyframes, Allocator alloc)
        {
            Keys = new NativeArray<Keyframe>(keyframes, alloc);
            owner = TRUE;
        }

        /// <summary>
        /// 从原生关键帧数组构造原生曲线（引用）
        /// 不拥有内存所有权
        /// </summary>
        /// <param name="keyframes">原生关键帧数组</param>
        public NativeCurve(NativeArray<Keyframe> keyframes)
        {
            Keys = keyframes;
            owner = FALSE;
        }

        /// <summary>
        /// 复制构造函数（引用）
        /// 不拥有内存所有权
        /// </summary>
        /// <param name="other">其他原生曲线</param>
        public NativeCurve(NativeCurve other)
        {
            Keys = other.Keys;
            owner = FALSE;
        }

        /// <summary>
        /// 复制构造函数（深拷贝）
        /// 拥有内存所有权
        /// </summary>
        /// <param name="other">其他原生曲线</param>
        /// <param name="alloc">内存分配器</param>
        public NativeCurve(NativeCurve other, Allocator alloc)
        {
            Keys = new NativeArray<Keyframe>(other.Keys, alloc);
            owner = TRUE;
        }

        /// <summary>
        /// 释放内存资源
        /// 只有拥有所有权时才释放
        /// </summary>
        public void Dispose()
        {
            if (owner != 0)
            {
                Keys.Dispose();
            }
        }

        /// <summary>
        /// 在指定时间点评估曲线值
        /// 使用线程安全的采样方法
        /// </summary>
        /// <param name="time">时间点</param>
        /// <returns>曲线值</returns>
        public float Evaluate(float time)
        {
            return CurveSampling.ThreadSafe.Evaluate(Keys, time);
        }

        /// <summary>关键帧数量</summary>
        public int Length => Keys.Length;
        /// <summary>曲线持续时间</summary>
        public float Duration => Keys[Length - 1].time - Keys[0].time;
    }

    /// <summary>
    /// 曲线采样工具类
    /// 提供线程安全的曲线值计算方法
    /// </summary>
    public static class CurveSampling
    {
        const float DefaultWeight = 0;

        /// <summary>
        /// 线程安全的曲线采样方法
        /// 支持多线程环境下的曲线值计算
        /// </summary>
        public static class ThreadSafe
        {
            /// <summary>
            /// 评估曲线在指定时间的值
            /// </summary>
            /// <param name="keys">关键帧数组</param>
            /// <param name="curveT">时间参数</param>
            /// <returns>曲线值</returns>
            public static float Evaluate(NativeArray<Keyframe> keys, float curveT)
            {
                return EvaluateWithinRange(keys, curveT, 0, keys.Length - 1);
            }

            /// <summary>
            /// 使用提示索引评估曲线值
            /// 可以提高连续采样的性能
            /// </summary>
            /// <param name="keys">关键帧数组</param>
            /// <param name="curveT">时间参数</param>
            /// <param name="hintIndex">提示索引（引用传递）</param>
            /// <returns>曲线值</returns>
            public static float EvaluateWithHint(NativeArray<Keyframe> keys, float curveT, ref int hintIndex)
            {
                int startIndex = 0;
                int endIndex = keys.Length - 1;
                if (endIndex <= hintIndex)
                    return keys[hintIndex].value;

                // 限制时间范围
                curveT = math.clamp(curveT, keys[hintIndex].time, keys[endIndex].time);
                FindIndexForSampling(keys, curveT, startIndex, endIndex, hintIndex, out int lhsIndex, out int rhsIndex);

                Keyframe lhs = keys[hintIndex];
                Keyframe rhs = keys[rhsIndex];
                return InterpolateKeyframe(lhs, rhs, curveT);
            }

            /// <summary>
            /// 在指定范围内评估曲线值
            /// </summary>
            /// <param name="keys">关键帧数组</param>
            /// <param name="curveT">时间参数</param>
            /// <param name="startIndex">起始索引</param>
            /// <param name="endIndex">结束索引</param>
            /// <returns>曲线值</returns>
            public static float EvaluateWithinRange(NativeArray<Keyframe> keys, float curveT, int startIndex,
                int endIndex)
            {
                if (endIndex <= startIndex)
                    return keys[startIndex].value;

                // 限制时间范围
                curveT = math.clamp(curveT, keys[startIndex].time, keys[endIndex].time);
                FindIndexForSampling(keys, curveT, startIndex, endIndex, -1, out int lhsIndex, out int rhsIndex);

                Keyframe lhs = keys[lhsIndex];
                Keyframe rhs = keys[rhsIndex];
                return InterpolateKeyframe(lhs, rhs, curveT);
            }

            /// <summary>
            /// 查找用于采样的关键帧索引
            /// 使用提示索引优化和二分查找来快速定位相邻的关键帧
            /// </summary>
            /// <param name="keys">关键帧数组</param>
            /// <param name="curveT">时间参数</param>
            /// <param name="start">搜索起始索引</param>
            /// <param name="end">搜索结束索引</param>
            /// <param name="hint">提示索引（-1表示无提示）</param>
            /// <param name="lhs">输出左侧关键帧索引</param>
            /// <param name="rhs">输出右侧关键帧索引</param>
            static void FindIndexForSampling(NativeArray<Keyframe> keys, float curveT, int start, int end,
                int hint,
                out int lhs, out int rhs)
            {
                if (hint != -1)
                {
                    hint = math.clamp(hint, start, end);
                    // 不能使用缓存时间，因为那是在未包装的时间空间中！
                    float time = keys[hint].time;

                    if (curveT > time)
                    {
                        const int kMaxLookahead = 3;
                        for (int i = 0; i < kMaxLookahead; i++)
                        {
                            int index = hint + i;
                            if (index + 1 < end && keys[index + 1].time > curveT)
                            {
                                lhs = index;
                                rhs = math.min(lhs + 1, end);
                                return;
                            }
                        }
                    }
                }

                // 回退到使用二分查找
                // 上界（第一个大于curveT的值）
                int __len = end - start;
                int __half;
                int __middle;
                int __first = start;
                while (__len > 0)
                {
                    __half = __len >> 1;
                    __middle = __first + __half;

                    var mid = keys[__middle];
                    if (curveT < mid.time)
                        __len = __half;
                    else
                    {
                        __first = __middle;
                        ++__first;
                        __len = __len - __half - 1;
                    }
                }

                // 如果不在范围内，我们选择最后一个元素两次
                lhs = __first - 1;
                rhs = math.min(end, __first);
            }

            /// <summary>
            /// 在两个关键帧之间进行插值
            /// 根据关键帧的权重模式选择合适的插值方法
            /// </summary>
            /// <param name="lhs">左侧关键帧</param>
            /// <param name="rhs">右侧关键帧</param>
            /// <param name="curveT">时间参数</param>
            /// <returns>插值结果</returns>
            public static float InterpolateKeyframe(Keyframe lhs, Keyframe rhs, float curveT)
            {
                float output;

                if ((lhs.weightedMode & WeightedMode.Out) != 0 || (rhs.weightedMode & WeightedMode.In) != 0)
                    output = BezierInterpolate(curveT, lhs, rhs);
                else
                    output = HermiteInterpolate(curveT, lhs, rhs);

                HandleSteppedCurve(lhs, rhs, ref output);

                return output;
            }

            /// <summary>
            /// Hermite插值方法
            /// 使用切线信息进行平滑插值
            /// </summary>
            /// <param name="curveT">时间参数</param>
            /// <param name="lhs">左侧关键帧</param>
            /// <param name="rhs">右侧关键帧</param>
            /// <returns>插值结果</returns>
            static float HermiteInterpolate(float curveT, Keyframe lhs, Keyframe rhs)
            {
                float dx = rhs.time - lhs.time;
                float m1;
                float m2;
                float t;
                if (dx != 0.0F)
                {
                    t = (curveT - lhs.time) / dx;
                    m1 = lhs.outTangent * dx;
                    m2 = rhs.inTangent * dx;
                }
                else
                {
                    t = 0.0F;
                    m1 = 0;
                    m2 = 0;
                }

                return HermiteInterpolate(t, lhs.value, m1, m2, rhs.value);
            }

            /// <summary>
            /// Hermite插值的数学实现
            /// 使用三次多项式进行平滑插值
            /// </summary>
            /// <param name="t">标准化时间参数(0-1)</param>
            /// <param name="p0">起始值</param>
            /// <param name="m0">起始切线</param>
            /// <param name="m1">结束切线</param>
            /// <param name="p1">结束值</param>
            /// <returns>插值结果</returns>
            static float HermiteInterpolate(float t, float p0, float m0, float m1, float p1)
            {
                float t2 = t * t;
                float t3 = t2 * t;

                float a = 2.0F * t3 - 3.0F * t2 + 1.0F;
                float b = t3 - 2.0F * t2 + t;
                float c = t3 - t2;
                float d = -2.0F * t3 + 3.0F * t2;

                return a * p0 + b * m0 + c * m1 + d * p1;
            }

            /// <summary>
            /// Bezier插值方法
            /// 使用权重信息进行更精确的插值控制
            /// </summary>
            /// <param name="curveT">时间参数</param>
            /// <param name="lhs">左侧关键帧</param>
            /// <param name="rhs">右侧关键帧</param>
            /// <returns>插值结果</returns>
            static float BezierInterpolate(float curveT, Keyframe lhs, Keyframe rhs)
            {
                float lhsOutWeight = (lhs.weightedMode & WeightedMode.Out) != 0 ? lhs.outWeight : DefaultWeight;
                float rhsInWeight = (rhs.weightedMode & WeightedMode.In) != 0 ? rhs.inWeight : DefaultWeight;

                float dx = rhs.time - lhs.time;
                if (dx == 0.0F)
                    return lhs.value;

                return BezierInterpolate((curveT - lhs.time) / dx, lhs.value, lhs.outTangent * dx, lhsOutWeight,
                    rhs.value, rhs.inTangent * dx, rhsInWeight);
            }

            static float FAST_CBRT_POSITIVE(float x)
            {
                return math.exp(math.log(x) / 3.0f);
            }

            static float FAST_CBRT(float x)
            {
                return (((x) < 0) ? -math.exp(math.log(-(x)) / 3.0f) : math.exp(math.log(x) / 3.0f));
            }

            static float BezierExtractU(float t, float w1, float w2)
            {
                float a = 3.0F * w1 - 3.0F * w2 + 1.0F;
                float b = -6.0F * w1 + 3.0F * w2;
                float c = 3.0F * w1;
                float d = -t;

                if (math.abs(a) > 1e-3f)
                {
                    float p = -b / (3.0F * a);
                    float p2 = p * p;
                    float p3 = p2 * p;

                    float q = p3 + (b * c - 3.0F * a * d) / (6.0F * a * a);
                    float q2 = q * q;

                    float r = c / (3.0F * a);
                    float rmp2 = r - p2;

                    float s = q2 + rmp2 * rmp2 * rmp2;

                    if (s < 0.0F)
                    {
                        float ssi = math.sqrt(-s);
                        float r_1 = math.sqrt(-s + q2);
                        float phi = math.atan2(ssi, q);

                        float r_3 = FAST_CBRT_POSITIVE(r_1);
                        float phi_3 = phi / 3.0F;

                        // Extract cubic roots.
                        float u1 = 2.0F * r_3 * math.cos(phi_3) + p;
                        float u2 = 2.0F * r_3 * math.cos(phi_3 + 2.0F * (float)math.PI / 3.0f) + p;
                        float u3 = 2.0F * r_3 * math.cos(phi_3 - 2.0F * (float)math.PI / 3.0f) + p;

                        if (u1 >= 0.0F && u1 <= 1.0F)
                            return u1;
                        else if (u2 >= 0.0F && u2 <= 1.0F)
                            return u2;
                        else if (u3 >= 0.0F && u3 <= 1.0F)
                            return u3;

                        // 旨在解决当 u 处于 [0,1] 之外时出现的数值不精确问题。
                        return (t < 0.5F) ? 0.0F : 1.0F;
                    }
                    else
                    {
                        float ss = math.sqrt(s);
                        float u = FAST_CBRT(q + ss) + FAST_CBRT(q - ss) + p;

                        if (u >= 0.0F && u <= 1.0F)
                            return u;

                        // 旨在解决当 u 处于 [0,1] 之外时出现的数值不精确问题。
                        return (t < 0.5F) ? 0.0F : 1.0F;
                    }
                }

                if (math.abs(b) > 1e-3f)
                {
                    float s = c * c - 4.0F * b * d;
                    float ss = math.sqrt(s);

                    float u1 = (-c - ss) / (2.0F * b);
                    float u2 = (-c + ss) / (2.0F * b);

                    if (u1 >= 0.0F && u1 <= 1.0F)
                        return u1;
                    else if (u2 >= 0.0F && u2 <= 1.0F)
                        return u2;

                    // 旨在解决当 u 处于 [0,1] 之外时出现的数值不精确问题。
                    return (t < 0.5F) ? 0.0F : 1.0F;
                }

                if (math.abs(c) > 1e-3f)
                {
                    return (-d / c);
                }

                return 0.0F;
            }

            static float BezierInterpolate(float t, float v1, float m1, float w1, float v2, float m2, float w2)
            {
                float u = BezierExtractU(t, w1, 1.0F - w2);
                return BezierInterpolate(u, v1, w1 * m1 + v1, v2 - w2 * m2, v2);
            }

            static float BezierInterpolate(float t, float p0, float p1, float p2, float p3)
            {
                float t2 = t * t;
                float t3 = t2 * t;
                float omt = 1.0F - t;
                float omt2 = omt * omt;
                float omt3 = omt2 * omt;

                return omt3 * p0 + 3.0F * t * omt2 * p1 + 3.0F * t2 * omt * p2 + t3 * p3;
            }

            /// <summary>
            /// 处理阶梯曲线
            /// 当切线为无穷大时，使用阶梯插值（保持左侧值）
            /// </summary>
            /// <param name="lhs">左侧关键帧</param>
            /// <param name="rhs">右侧关键帧</param>
            /// <param name="value">插值结果（引用传递）</param>
            static void HandleSteppedCurve(Keyframe lhs, Keyframe rhs, ref float value)
            {
                if (float.IsInfinity(lhs.outTangent) || float.IsInfinity(rhs.inTangent))
                    value = lhs.value;
            }
        }
    }
}