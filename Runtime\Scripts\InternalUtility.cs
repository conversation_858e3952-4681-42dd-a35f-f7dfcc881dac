﻿using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Splines;

namespace UI_Spline_Renderer
{
    /// <summary>
    /// 内部工具类
    /// 提供UI样条线渲染器内部使用的各种工具方法和扩展方法
    /// 使用Burst编译优化性能
    /// </summary>
    [BurstCompile]
    internal static class InternalUtility
    {
        /// <summary>
        /// 获取RectTransform的轴心点在世界空间中的位置
        /// 将本地轴心点坐标转换为世界坐标
        /// </summary>
        /// <param name="source">目标RectTransform</param>
        /// <returns>轴心点的世界坐标</returns>
        // source of this methods
        // https://forum.unity.com/threads/moving-just-recttransform-pivot-in-world-space.1380249/
        internal static Vector3 GetPivotInWorldSpace(this RectTransform source)
        {
            var pivot = new Vector2(
                source.rect.xMin + source.pivot.x * source.rect.width,
                source.rect.yMin + source.pivot.y * source.rect.height);

            return source.TransformPoint(new Vector3(pivot.x, pivot.y, 0f));
        }

        /// <summary>
        /// 设置RectTransform的轴心点而不改变矩形大小
        /// 在保持视觉位置不变的情况下调整轴心点位置
        /// </summary>
        /// <param name="source">目标RectTransform</param>
        /// <param name="pivot">新的轴心点世界坐标</param>
        // source of this methods
        // https://forum.unity.com/threads/moving-just-recttransform-pivot-in-world-space.1380249/
        internal static void SetPivotWithoutRect(this RectTransform source, Vector3 pivot)
        {
            var rect = source.rect;
            if(float.IsNaN(rect.x) || float.IsNaN(rect.y) || float.IsNaN(rect.size.x) || float.IsNaN(rect.size.y)) return;
            pivot = source.InverseTransformPoint(pivot);
            var pivot2 = new Vector2(
                (pivot.x - rect.xMin) / rect.width,
                (pivot.y - rect.yMin) / rect.height);

            var offset = pivot2 - source.pivot;
            offset.Scale(source.rect.size);
            var worldPos = source.position + source.TransformVector(offset);

            source.pivot = pivot2;
            source.position = worldPos;
        }

        /// <summary>
        /// 将值从一个范围重新映射到另一个范围
        /// 线性插值映射，常用于数值范围转换
        /// </summary>
        /// <param name="value">要映射的值</param>
        /// <param name="beforeRangeMin">原始范围最小值</param>
        /// <param name="beforeRangeMax">原始范围最大值</param>
        /// <param name="targetRangeMin">目标范围最小值</param>
        /// <param name="targetRangeMax">目标范围最大值</param>
        /// <returns>映射后的值</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static float Remap(this float value, float beforeRangeMin, float beforeRangeMax, float targetRangeMin, float targetRangeMax)
        {
            var denominator = beforeRangeMax - beforeRangeMin;
            if (denominator == 0) return targetRangeMin;

            var ratio = (value - beforeRangeMin) / denominator;
            var result = (targetRangeMax - targetRangeMin) * ratio + targetRangeMin;
            return result;
        }

        /// <summary>
        /// 将整数值从一个范围重新映射到另一个范围
        /// 整数版本的重映射方法
        /// </summary>
        internal static float Remap(this int value, float beforeRangeMin, float beforeRangeMax, float targetRangeMin, float targetRangeMax)
        {
            return Remap((float)value, beforeRangeMin, beforeRangeMax, targetRangeMin, targetRangeMax);
        }

        /// <summary>计算浮点数的平方</summary>
        internal static float sqr(this float value) => value * value;

        /// <summary>
        /// 计算线性插值点
        /// 返回值在min和max之间的标准化位置(0-1)
        /// </summary>
        /// <param name="value">当前值</param>
        /// <param name="min">最小值</param>
        /// <param name="max">最大值</param>
        /// <returns>标准化位置</returns>
        internal static float LerpPoint(float value, float min, float max)
        {
            return (value - min) / (max - min);
        }

        /// <summary>
        /// 获取当前精灵对应的起始/结束图像预设类型
        /// 通过比较精灵引用确定使用的是哪种预设类型
        /// </summary>
        /// <param name="target">要检查的精灵</param>
        /// <returns>对应的预设类型</returns>
        internal static StartEndImagePreset GetCurrentStartImagePreset(Sprite target)
        {
            if (target == null)              return StartEndImagePreset.None;
            if (target == UISplineRendererSettings.Instance.triangleHead)     return StartEndImagePreset.Triangle;
            if (target == UISplineRendererSettings.Instance.arrowHead)        return StartEndImagePreset.Arrow;
            if (target == UISplineRendererSettings.Instance.emptyCircleHead)  return StartEndImagePreset.EmptyCircle;
            if (target == UISplineRendererSettings.Instance.filledCircleHead) return StartEndImagePreset.FilledCircle;

            return StartEndImagePreset.Custom;
        }

        /// <summary>
        /// 计算样条线渲染所需的顶点数量
        /// 根据样条线长度、段长度和渲染范围计算顶点总数
        /// </summary>
        /// <param name="spline">样条线</param>
        /// <param name="segmentLength">段长度</param>
        /// <param name="renderRange">渲染范围</param>
        /// <returns>所需顶点数量</returns>
        internal static int CalcVertexCount(this Spline spline, float segmentLength, Vector2 renderRange)
        {
            var length = spline.GetLength() * (renderRange.y - renderRange.x);
            return Mathf.Max(Mathf.CeilToInt(length * segmentLength), 1) * 2 + 4;
        }

        /// <summary>
        /// 计算原生样条线渲染所需的顶点数量（Burst优化版本）
        /// 使用Unity Mathematics进行高性能计算
        /// </summary>
        /// <param name="spline">原生样条线</param>
        /// <param name="segmentLength">段长度</param>
        /// <param name="renderRange">渲染范围</param>
        /// <returns>所需顶点数量</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        internal static int CalcVertexCount(this NativeSpline spline, float segmentLength, float2 renderRange)
        {
            var length = spline.GetLength() * (renderRange.y - renderRange.x);
            return math.max((int)math.ceil(length * segmentLength), 1) * 2 + 4;
        }


        /// <summary>
        /// 将Unity的Gradient转换为原生颜色渐变
        /// 用于在Job System中高效处理颜色渐变计算
        /// </summary>
        /// <param name="gradient">Unity颜色渐变</param>
        /// <param name="allocator">内存分配器类型</param>
        /// <returns>原生颜色渐变结构</returns>
        internal static NativeColorGradient ToNative (this Gradient gradient, Allocator allocator = Allocator.TempJob)
        {
            var aKeys = new NativeArray<float2>(gradient.alphaKeys.Length, allocator);
            for (int i = 0; i < gradient.alphaKeys.Length; i++)
            {
                var key = gradient.alphaKeys[i];
                aKeys[i] = new float2(key.alpha, key.time);
            }

            var cKeys = new NativeArray<float4>(gradient.colorKeys.Length, allocator);
            for (int i = 0; i < gradient.colorKeys.Length; i++)
            {
                var key = gradient.colorKeys[i];
                cKeys[i] = new float4(key.color.r, key.color.g, key.color.b, key.time);
            }


            var native = new NativeColorGradient()
            {
                alphaKeyFrames = aKeys,
                colorKeyFrames = cKeys
            };

            return native;
        }

        /// <summary>
        /// 挤出样条线边缘生成顶点对
        /// 根据样条线的位置、切线和法线信息生成用于渲染的顶点数据
        /// 支持面向屏幕和保持Z轴为0的选项
        /// </summary>
        /// <param name="w">宽度</param>
        /// <param name="V">V坐标（纹理坐标）</param>
        /// <param name="clr">顶点颜色</param>
        /// <param name="pos">位置（引用传递，可能被修改）</param>
        /// <param name="tan">切线方向</param>
        /// <param name="up">上方向</param>
        /// <param name="keepBillboard">是否保持面向屏幕</param>
        /// <param name="keepZeroZ">是否保持Z轴为0</param>
        /// <param name="uvMultiplier">UV乘数</param>
        /// <param name="uvOffset">UV偏移</param>
        /// <param name="v0">输出的第一个顶点</param>
        /// <param name="v1">输出的第二个顶点</param>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        [BurstCompile]
        internal static void ExtrudeEdge(
            float w, float V, in Color clr, ref float3 pos, in float3 tan, in float3 up,
            bool keepBillboard, bool keepZeroZ, in float2 uvMultiplier, in float2 uvOffset,
            out UIVertex v0, out UIVertex v1)
        {
            var perpendicular =
                keepBillboard ? math.normalizesafe(math.cross(tan, new float3(0, 0, -1))) : math.normalizesafe(math.cross(tan, up));

            if (keepZeroZ)
            {
                pos.z = 0;
            }

            var uv = new float2(0, V) * uvMultiplier - uvOffset;
            var vert = new UIVertex
            {
                position = pos + perpendicular * w * 0.5f,
                uv0 = new Vector4(uv.x, uv.y),
                color = clr
            };

            v0 = vert;

            uv = new float2(1, V) * uvMultiplier - uvOffset;
            vert = new UIVertex
            {
                position = pos - perpendicular * w * 0.5f,
                uv0 = new Vector4(uv.x, uv.y),
                color = clr
            };

            v1 = vert;
        }
    }
}