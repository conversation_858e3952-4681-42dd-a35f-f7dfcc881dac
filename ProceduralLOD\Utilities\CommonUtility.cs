#if UNITY_EDITOR

using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using UnityEditor;
using UnityEngine;
using Debug = UnityEngine.Debug;
using Object = UnityEngine.Object;

namespace ProceduralLOD
{
    public static class CommonUtility
    {
        [MenuItem("GameObject/地编/生成网格LOD")]
        public static void CreateSimplifierLODForSelection()
        {
            foreach (var go in Selection.gameObjects)
            {
                CreateSimplifierLOD(go);
            }
        }

        [MenuItem("自定义工具/美术专用/打印未使用的程序网格")]
        private static void LogUnusedSimplifierMesh()
        {
            // HashSet<Mesh> meshesSet = new HashSet<Mesh>();
            // foreach (var renderer in UnityEngine.Object.FindObjectsByType<Renderer>(FindObjectsSortMode.None))
            // {
            //     Mesh sharedMesh = null;
            //     if (renderer is MeshRenderer mr)
            //     {
            //         MeshFilter mf = mr.GetComponent<MeshFilter>();
            //         if (mf)
            //             sharedMesh = mf.sharedMesh;
            //     }
            //
            //     meshesSet.Add(sharedMesh);
            // }
            //
            // string[] guids = AssetDatabase.FindAssets("t:Mesh", new string[] { Path.Combine(GetAssetDirByPrefab(), MeshSaveFolder)  });
            // for (int i = 0; i < guids.Length; i++)
            // {
            //     string guid = guids[i];
            //     string file = AssetDatabase.GUIDToAssetPath(guid);
            //     Mesh refMesh = AssetDatabase.LoadAssetAtPath<Mesh>(file);
            //     if (!meshesSet.Contains(refMesh))
            //     {
            //         Debug.Log(file, refMesh);
            //     }
            // }
        }

        public static Action onSequenceCompleted;

        private static readonly string MeshSaveFolder = "Models/AutoLODMeshes";
        private static readonly string TextureSaveFolder = "Textures/AutoLODTextures";
        private static readonly string MaterialSaveFolder = "Materials/AutoLODMaterials";
        
        public static void RunSequenceCoroutine(params IEnumerator[] enumerators)
        {
            CoroutineRunner.RunCoroutine(SequenceBuilding(enumerators));
        }

        private static IEnumerator SequenceBuilding(IEnumerator[] enumerators)
        {
            Stopwatch watch = Stopwatch.StartNew();
            LODMaterialUtility.ClearCache();
            LODTextureUtility.ClearCache();
            for (int i = 0; i < enumerators.Length; i++)
            {
                yield return enumerators[i];
                if (enumerators.Length > 1)
                    Debug.Log($"任务进度：{i + 1} / {enumerators.Length}");
            }

            watch.Stop();
            
            onSequenceCompleted?.Invoke();
            onSequenceCompleted = null;
            
            Debug.Log($"任务完成：时长：{watch.Elapsed.Minutes}分{watch.Elapsed.Seconds % 60}秒");
        }

        public static Mesh GetMeshByRenderer(Renderer renderer)
        {
            Mesh mesh = null;
            if (renderer is SkinnedMeshRenderer smr)
            {
                mesh = smr.sharedMesh;
            }
            else if (renderer is MeshRenderer mr)
            {
                var filter = mr.GetComponent<MeshFilter>();
                mesh = filter.sharedMesh;
            }

            return mesh;
        }

        public static void SetMeshByRenderer(Renderer renderer, Mesh mesh)
        {
            if (renderer is SkinnedMeshRenderer smr)
            {
                smr.sharedMesh = mesh;
            }
            else if (renderer is MeshRenderer mr)
            {
                var filter = mr.GetComponent<MeshFilter>();
                filter.sharedMesh = mesh;
            }
        }

        public static void SaveSimplifierAssets(GameObject prefab)
        {
            string dir = GetAssetRootDir(prefab);
            LODGroup lodGroup = prefab.GetComponent<LODGroup>();
            LOD[] lods = lodGroup.GetLODs();
            for (int i = 0; i < lods.Length; i++)
            {
                LOD lod = lods[i];
                foreach (var renderer in lod.renderers)
                {
                    Mesh mesh = GetMeshByRenderer(renderer);
                    if (SaveSimplifierAsset(dir, ref mesh, "_LOD" + i))
                    {
                        SetMeshByRenderer(renderer, mesh);
                    }

                    Material[] materials = renderer.sharedMaterials;
                    for (int j = 0; j < materials.Length; j++)
                    {
                        Material material = materials[j];
                        if (material == null)
                            continue;

                        Dictionary<string, Texture2D> texdict = new Dictionary<string, Texture2D>();
                        foreach (var texname in material.GetTexturePropertyNames())
                        {
                            if (material.HasTexture(texname))
                            {
                                Texture2D texture = material.GetTexture(texname) as Texture2D;
                                texdict.Add(texname, texture);
                            }
                        }

                        foreach (var texname in texdict.Keys)
                        {
                            Texture2D texture = texdict[texname];
                            if (SaveSimplifierAsset(dir, ref texture, "_LOD" + i))
                            {
                                material.SetTexture(texname, texture);
                            }
                        }
                        
                        if (SaveSimplifierAsset(dir, ref material, "_LOD" + i))
                        {
                            materials[j] = material;
                        }
                    }

                    renderer.sharedMaterials = materials;
                }
            }
           
        }
        
        public static bool SaveSimplifierAsset<T>(string rootDir, ref T asset, string postfixed = "") where T : Object
        {
            if (asset == null || AssetDatabase.IsNativeAsset(asset))
            {
                return false;
            }
            
            string path = AssetDatabase.GetAssetPath(asset);
            if (!string.IsNullOrEmpty(path))
                return false;

            asset.name += postfixed;
            bool saved = false;
            
            if (asset is Texture2D texture)
            {
                string sourceId = LODTextureUtility.GetSourceID(texture);
                string dir = Path.Combine(rootDir, TextureSaveFolder);
                if (!Directory.Exists(dir))
                {
                    Directory.CreateDirectory(dir);
                }
                byte[] bytes = texture.EncodeToTGA();
                path = Path.Combine(dir, texture.name + ".tga").Replace("\\", "/");
                File.WriteAllBytes(path, bytes);
                AssetDatabase.ImportAsset(path);
                asset = AssetDatabase.LoadAssetAtPath<T>(path);
                LODTextureUtility.SetCache(sourceId, asset);
                saved = true;
            }
            else
            {
                if (asset is Mesh mesh)
                {
                    string dir = Path.Combine(rootDir, MeshSaveFolder);
                    path = Path.Combine(dir, asset.name + ".mesh").Replace("\\", "/");
                    asset = CreateAsset(asset, path);
                    saved = true;
                }
                else if (asset is Material material)
                {
                    string sourceId = LODMaterialUtility.GetSourceID(material);
                    string dir = Path.Combine(rootDir, MaterialSaveFolder);
                    path = Path.Combine(dir, asset.name + ".mat").Replace("\\", "/");
                    asset = CreateAsset(asset, path);
                    LODMaterialUtility.SetCache(sourceId, asset);
                    saved = true;
                }
            }

            return saved;
        }
        
        private static string GetAssetRootDir(Object asset, params string[] combines)
        {
            string file;
            if (asset is GameObject go)
            {
                if (!PrefabUtility.IsPartOfAnyPrefab(go))
                {
                    go = go.GetComponentInParent<LODGenerateHelper>().gameObject;
                }
                file = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(go);
            }
            else
            {
                file = AssetDatabase.GetAssetPath(asset);
            }

            string dir = Path.GetDirectoryName(file).Replace("\\", "/");
            const string RootFolder = "Objects/";
            int idx = dir.IndexOf(RootFolder);
            if (idx != -1)
            {
                dir = dir.Substring(0, idx + RootFolder.Length);
            }

            dir = Path.Combine(dir, string.Join("/", combines));
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);
            return dir;
        }

        public static GameObject CreateSampleObject(GameObject root, Renderer[] renderers)
        {
            GameObject sample = new GameObject("Sample");
            sample.hideFlags = HideFlags.DontSave;
            for (int i = 0; i < renderers.Length; i++)
            {
                GameObject obj = new GameObject();
                obj.AddComponent<MeshFilter>().sharedMesh = CommonUtility.GetMeshByRenderer(renderers[i]); 
                obj.AddComponent<MeshRenderer>().sharedMaterials = renderers[i].sharedMaterials;
                obj.transform.SetParent(sample.transform);
                obj.transform.position = renderers[i].transform.position - root.transform.position;
                obj.transform.rotation = renderers[i].transform.localRotation;
                obj.transform.localScale = new Vector3
                (
                    root.transform.lossyScale.x / renderers[i].transform.lossyScale.x,
                    root.transform.lossyScale.y / renderers[i].transform.lossyScale.y,
                    root.transform.lossyScale.z / renderers[i].transform.lossyScale.z
                );
            }

            return sample;
        }

        public static void CreateSimplifierLOD(GameObject go)
        {
            if (go.GetComponent<LODGroup>())
            {
                return;
            }

            LODGenerateHelper helper = go.GetComponent<LODGenerateHelper>();
            if (helper == null)
                helper = go.AddComponent<LODGenerateHelper>();
            helper.Build();
        }

        public static float CalculateProjectionSize(Renderer[] renderers)
        {
            Bounds bounds = CalculateBounds(renderers);
            return CalculateProjectionSize(bounds);
        }

        public static float CalculateProjectionSize(Bounds bounds)
        {
            Vector3 size = bounds.size;
            return Mathf.Max(Mathf.Max(size.x, size.z) , size.y);
        }

        public static Bounds CalculateBounds(GameObject go)
        {
            return CalculateBounds(go.GetComponentsInChildren<Renderer>());
        }

        public static Bounds CalculateBounds(Renderer[] renderers)
        {
            Bounds bounds = new Bounds(Vector3.zero, Vector3.zero);
            foreach (var renderer in renderers)
            {
                if (renderer == null)
                {
                    continue;
                }

                if (bounds.size == Vector3.zero)
                {
                    bounds = renderer.bounds;
                    continue;
                }

                bounds.Encapsulate(renderer.bounds);
            }

            return bounds;
        }
        
        public static T CreateAsset<T>(T obj, string path) where T : Object
        {
            int splitPos = obj.name.LastIndexOf("/");
            if (splitPos != -1)
            {
                obj.name = obj.name.Substring(splitPos + 1);
            }

            string dir = Path.GetDirectoryName(path);
            if (!Directory.Exists(dir))
            {
                Directory.CreateDirectory(dir);
            }
            
            string name = Path.GetFileNameWithoutExtension(path);

            T dest = AssetDatabase.LoadAssetAtPath<T>(path);
            if ((Object) dest != (Object) null)
            {
                string uniquePath = AssetDatabase.GenerateUniqueAssetPath(path);
                uniquePath = uniquePath.Replace(" ", "");
                AssetDatabase.CreateAsset((Object) obj, uniquePath);
                AssetDatabase.ImportAsset(uniquePath);
                File.Delete(path);
                File.Move(uniquePath, path);
            }
            else
            {
                AssetDatabase.CreateAsset((Object) obj, path);
            }
            AssetDatabase.ImportAsset(path);
            obj = AssetDatabase.LoadAssetAtPath<T>(path);
            obj.name = name;

            return obj;
        }
    }
}

#endif